package db

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupWalletTxStatsTable creates the wallet_tx_stats table for testing
func setupWalletTxStatsTable(ctx context.Context, repo *Repo) error {
	query := `
		CREATE TABLE IF NOT EXISTS wallet_tx_stats (
			wallet BYTEA NOT NULL PRIMARY KEY,
			max_tx_in_1h BIGINT NOT NULL DEFAULT 0,
			total_tx_count BIGINT NOT NULL DEFAULT 0,
			last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
		);

		CREATE INDEX IF NOT EXISTS idx_wallet_tx_stats_max_tx_in_1h ON wallet_tx_stats (max_tx_in_1h DESC);
		CREATE INDEX IF NOT EXISTS idx_wallet_tx_stats_total_tx_count ON wallet_tx_stats (total_tx_count DESC);
		CREATE INDEX IF NOT EXISTS idx_wallet_tx_stats_last_updated ON wallet_tx_stats (last_updated);
	`
	_, err := repo.pool.Exec(ctx, query)
	return err
}

// createTransactionSignaturesTable creates a basic transaction_signatures table for testing
func createTransactionSignaturesTable(ctx context.Context, repo *Repo) error {
	query := `
		CREATE TABLE IF NOT EXISTS transaction_signatures (
			slot BIGINT NOT NULL,
			tx_index INTEGER NOT NULL,
			signature BYTEA NOT NULL,
			signers BYTEA[],
			PRIMARY KEY (slot, tx_index)
		) PARTITION BY RANGE (slot);
	`
	_, err := repo.pool.Exec(ctx, query)
	return err
}

// TestWalletTxStatsUpsert tests the upsert functionality for wallet transaction stats
func TestWalletTxStatsUpsert(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupWalletTxStatsTestDB(t, repo)
	}()

	// Setup test tables
	err = setupWalletTxStatsTable(ctx, repo)
	require.NoError(t, err)

	// Create test wallet transaction stats
	wallet1 := []byte("wallet1234567890123456789012345678901")
	wallet2 := []byte("wallet2345678901234567890123456789012")
	
	now := time.Now().Truncate(time.Second)
	
	testStats := []WalletTxStats{
		{
			Wallet:       wallet1,
			MaxTxIn1h:    50,
			TotalTxCount: 1000,
			LastUpdated:  now,
		},
		{
			Wallet:       wallet2,
			MaxTxIn1h:    25,
			TotalTxCount: 500,
			LastUpdated:  now,
		},
	}

	// First upsert
	err = repo.UpsertWalletTxStats(ctx, testStats)
	require.NoError(t, err)

	// Verify data was inserted
	topByTotal, err := repo.GetTopWalletsByTxCount(ctx, 10)
	require.NoError(t, err)
	require.Len(t, topByTotal, 2)

	// Check the results
	assert.Equal(t, wallet1, topByTotal[0].Wallet)
	assert.Equal(t, int64(50), topByTotal[0].MaxTxIn1h)
	assert.Equal(t, int64(1000), topByTotal[0].TotalTxCount)

	assert.Equal(t, wallet2, topByTotal[1].Wallet)
	assert.Equal(t, int64(25), topByTotal[1].MaxTxIn1h)
	assert.Equal(t, int64(500), topByTotal[1].TotalTxCount)

	// Update existing records (should accumulate total_tx_count and take max of max_tx_in_1h)
	updateStats := []WalletTxStats{
		{
			Wallet:       wallet1,
			MaxTxIn1h:    30, // Lower than existing, should keep 50
			TotalTxCount: 200, // Should add to existing 1000
			LastUpdated:  now.Add(time.Hour),
		},
		{
			Wallet:       wallet2,
			MaxTxIn1h:    75, // Higher than existing, should update to 75
			TotalTxCount: 300, // Should add to existing 500
			LastUpdated:  now.Add(time.Hour),
		},
	}

	err = repo.UpsertWalletTxStats(ctx, updateStats)
	require.NoError(t, err)

	// Verify updates
	topByTotal, err = repo.GetTopWalletsByTxCount(ctx, 10)
	require.NoError(t, err)
	require.Len(t, topByTotal, 2)

	// wallet1 should still have max_tx_in_1h=50 and total_tx_count=1200
	assert.Equal(t, wallet1, topByTotal[0].Wallet)
	assert.Equal(t, int64(50), topByTotal[0].MaxTxIn1h)
	assert.Equal(t, int64(1200), topByTotal[0].TotalTxCount)

	// wallet2 should have max_tx_in_1h=75 and total_tx_count=800
	assert.Equal(t, wallet2, topByTotal[1].Wallet)
	assert.Equal(t, int64(75), topByTotal[1].MaxTxIn1h)
	assert.Equal(t, int64(800), topByTotal[1].TotalTxCount)
}

// TestGetTopWalletsByMaxTxIn1h tests querying top wallets by max transactions in 1 hour
func TestGetTopWalletsByMaxTxIn1h(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupWalletTxStatsTestDB(t, repo)
	}()

	// Setup test tables
	err = setupWalletTxStatsTable(ctx, repo)
	require.NoError(t, err)

	// Create test data with different max_tx_in_1h values
	now := time.Now().Truncate(time.Second)
	testStats := []WalletTxStats{
		{
			Wallet:       []byte("low_activity_wallet123456789012345678"),
			MaxTxIn1h:    10,
			TotalTxCount: 100,
			LastUpdated:  now,
		},
		{
			Wallet:       []byte("high_activity_wallet12345678901234567"),
			MaxTxIn1h:    500,
			TotalTxCount: 1000,
			LastUpdated:  now,
		},
		{
			Wallet:       []byte("medium_activity_wallet1234567890123456"),
			MaxTxIn1h:    100,
			TotalTxCount: 2000,
			LastUpdated:  now,
		},
	}

	err = repo.UpsertWalletTxStats(ctx, testStats)
	require.NoError(t, err)

	// Get top wallets by max tx in 1h
	topByMaxTx, err := repo.GetTopWalletsByMaxTxIn1h(ctx, 3)
	require.NoError(t, err)
	require.Len(t, topByMaxTx, 3)

	// Should be ordered by max_tx_in_1h DESC
	assert.Equal(t, int64(500), topByMaxTx[0].MaxTxIn1h)
	assert.Equal(t, int64(100), topByMaxTx[1].MaxTxIn1h)
	assert.Equal(t, int64(10), topByMaxTx[2].MaxTxIn1h)

	// Test with limit
	topByMaxTx, err = repo.GetTopWalletsByMaxTxIn1h(ctx, 1)
	require.NoError(t, err)
	require.Len(t, topByMaxTx, 1)
	assert.Equal(t, int64(500), topByMaxTx[0].MaxTxIn1h)
}

// TestBackfillWalletTxStatsPartitionCalculation tests the partition range calculation
func TestBackfillWalletTxStatsPartitionCalculation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupWalletTxStatsTestDB(t, repo)
	}()

	// Setup test tables
	err = createTransactionSignaturesTable(ctx, repo)
	require.NoError(t, err)
	err = setupWalletTxStatsTable(ctx, repo)
	require.NoError(t, err)

	// Create some transaction_signatures partitions for testing
	testSlots := []uint64{100, 216000, 432000, 648000}
	for _, slot := range testSlots {
		err := repo.EnsureNewPartitionExists(ctx, "transaction_signatures", slot)
		require.NoError(t, err)
	}

	// Test getting partitions in range
	partitions, err := repo.getPartitionsInRange(ctx, "transaction_signatures", 200000, 500000)
	require.NoError(t, err)

	// Should include partitions that overlap with range [200000, 500000)
	// p0: [0, 216000) - overlaps
	// p1: [216000, 432000) - overlaps
	// p2: [432000, 648000) - overlaps
	// p3: [648000, 864000) - no overlap
	assert.GreaterOrEqual(t, len(partitions), 2) // At least p1 and p2

	var foundP1, foundP2 bool
	for _, p := range partitions {
		if p.PartitionName == "transaction_signatures_p1" {
			foundP1 = true
			assert.Equal(t, int64(216000), p.StartSlot)
			assert.Equal(t, int64(432000), p.EndSlot)
		}
		if p.PartitionName == "transaction_signatures_p2" {
			foundP2 = true
			assert.Equal(t, int64(432000), p.StartSlot)
			assert.Equal(t, int64(648000), p.EndSlot)
		}
	}
	assert.True(t, foundP1, "Should find partition p1")
	assert.True(t, foundP2, "Should find partition p2")
}

// TestEmptyWalletTxStatsOperations tests operations with empty data
func TestEmptyWalletTxStatsOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupWalletTxStatsTestDB(t, repo)
	}()

	// Setup test tables
	err = setupWalletTxStatsTable(ctx, repo)
	require.NoError(t, err)

	// Test upsert with empty slice
	err = repo.UpsertWalletTxStats(ctx, []WalletTxStats{})
	require.NoError(t, err)

	// Test queries on empty table
	topByTotal, err := repo.GetTopWalletsByTxCount(ctx, 10)
	require.NoError(t, err)
	assert.Empty(t, topByTotal)

	topByMaxTx, err := repo.GetTopWalletsByMaxTxIn1h(ctx, 10)
	require.NoError(t, err)
	assert.Empty(t, topByMaxTx)

	// Test backfill with no partitions
	err = repo.BackfillWalletTxStats(ctx, 100, 200)
	require.NoError(t, err) // Should not error even with no data
}

// TestWalletTxStatsTypes tests data type handling
func TestWalletTxStatsTypes(t *testing.T) {
	// Test boundary values
	testStats := []WalletTxStats{
		{
			Wallet:       make([]byte, 32), // 32 byte wallet address
			MaxTxIn1h:    0,
			TotalTxCount: 0,
			LastUpdated:  time.Now(),
		},
		{
			Wallet:       make([]byte, 32),
			MaxTxIn1h:    9223372036854775807, // Max int64
			TotalTxCount: 9223372036854775807, // Max int64
			LastUpdated:  time.Now(),
		},
	}

	// Fill wallet addresses with test data
	for i := range testStats[0].Wallet {
		testStats[0].Wallet[i] = byte(i)
		testStats[1].Wallet[i] = byte(255 - i)
	}

	// Validate struct values
	assert.Len(t, testStats[0].Wallet, 32)
	assert.Len(t, testStats[1].Wallet, 32)
	assert.Equal(t, int64(0), testStats[0].MaxTxIn1h)
	assert.Equal(t, int64(9223372036854775807), testStats[1].MaxTxIn1h)
}

// BenchmarkUpsertWalletTxStats benchmarks the upsert performance
func BenchmarkUpsertWalletTxStats(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	if err != nil {
		b.Skip("Database not available for benchmark")
	}
	defer repo.Close()

	// Create test data
	batchSize := 1000
	testStats := make([]WalletTxStats, batchSize)
	now := time.Now()

	for i := 0; i < batchSize; i++ {
		wallet := make([]byte, 32)
		for j := range wallet {
			wallet[j] = byte((i + j) % 256)
		}
		testStats[i] = WalletTxStats{
			Wallet:       wallet,
			MaxTxIn1h:    int64(i % 1000),
			TotalTxCount: int64(i * 10),
			LastUpdated:  now,
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := repo.UpsertWalletTxStats(ctx, testStats)
		if err != nil {
			b.Fatalf("Upsert failed: %v", err)
		}
	}
}

// BenchmarkGetTopWallets benchmarks the query performance
func BenchmarkGetTopWallets(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data_test"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	if err != nil {
		b.Skip("Database not available for benchmark")
	}
	defer repo.Close()

	// Prepare test data
	testStats := make([]WalletTxStats, 10000)
	now := time.Now()

	for i := 0; i < len(testStats); i++ {
		wallet := make([]byte, 32)
		for j := range wallet {
			wallet[j] = byte((i + j) % 256)
		}
		testStats[i] = WalletTxStats{
			Wallet:       wallet,
			MaxTxIn1h:    int64(i % 1000),
			TotalTxCount: int64(i * 10),
			LastUpdated:  now,
		}
	}

	// Setup test tables for benchmark
	err = setupWalletTxStatsTable(ctx, repo)
	if err != nil {
		b.Fatalf("Table setup failed: %v", err)
	}

	err = repo.UpsertWalletTxStats(ctx, testStats)
	if err != nil {
		b.Fatalf("Setup failed: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := repo.GetTopWalletsByTxCount(ctx, 100)
		if err != nil {
			b.Fatalf("Query failed: %v", err)
		}
	}
}

// Helper function to clean up test data
func cleanupWalletTxStatsTestDB(t *testing.T, repo *Repo) {
	ctx := context.Background()
	
	// Clean up wallet_tx_stats table
	_, err := repo.pool.Exec(ctx, "DROP TABLE IF EXISTS wallet_tx_stats CASCADE")
	if err != nil {
		t.Logf("Failed to cleanup wallet_tx_stats table: %v", err)
	}
	
	// Clean up transaction_signatures partitions
	_, err = repo.pool.Exec(ctx, `
		DO $$ 
		DECLARE
			partition_name TEXT;
		BEGIN
			FOR partition_name IN 
				SELECT tablename FROM pg_tables 
				WHERE tablename LIKE 'transaction_signatures_p%' AND schemaname = 'public'
			LOOP
				EXECUTE 'DROP TABLE IF EXISTS ' || partition_name || ' CASCADE';
			END LOOP;
		END $$;
	`)
	if err != nil {
		t.Logf("Failed to cleanup transaction_signatures partitions: %v", err)
	}
	
	// Clean up main transaction_signatures table
	_, err = repo.pool.Exec(ctx, "DROP TABLE IF EXISTS transaction_signatures CASCADE")
	if err != nil {
		t.Logf("Failed to cleanup transaction_signatures table: %v", err)
	}
}